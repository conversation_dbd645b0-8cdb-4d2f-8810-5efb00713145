# تقييد خيارات إدارة التذاكر لأدوار الدعم النفسي

## التغييرات المطبقة

تم تطبيق نظام صلاحيات جديد لتقييد الخيارات التالية لأدوار الدعم النفسي المحددة فقط:

### الخيارات المقيدة:
1. **إضافة عضو** - إضافة عضو إلى التذكرة
2. **إزالة عضو** - إزالة عضو من التذكرة  
3. **إغلاق التذكرة** - إغلاق التذكرة نهائياً
4. **إرسال تذكير** - إرسال تذكير للعضو في الخاص

### الملفات المعدلة:

#### 1. `utils/permissionHelper.js` (ملف جديد)
- دوال التحقق من صلاحيات أدوار الدعم النفسي
- `hasTicketSupportRole()` - التحقق من الصلاحيات باستخدام معرف القناة
- `hasTicketSupportRoleWithPanel()` - التحقق من الصلاحيات مع بيانات اللوحة المتوفرة
- `sendPermissionDeniedMessage()` - إرسال رسالة خطأ للمستخدمين غير المخولين

#### 2. `utils/ticketManager.js`
- **إنشاء التذكرة**: تعديل قائمة الخيارات لتظهر فقط للمستخدمين المخولين
- **إغلاق التذكرة**: إضافة التحقق من الصلاحيات قبل الإغلاق
- **إضافة عضو**: إضافة التحقق من الصلاحيات قبل الإضافة
- **إزالة عضو**: إضافة التحقق من الصلاحيات قبل الإزالة

#### 3. `components/selectMenus/selectTicketActions.js`
- إضافة التحقق من الصلاحيات قبل تنفيذ أي إجراء مقيد
- معالجة خيار "غير مخول" للمستخدمين غير المخولين
- تنظيف الكود من المتغيرات غير المستخدمة

## كيفية عمل النظام:

### 1. عند إنشاء التذكرة:
- يتم التحقق من أن المستخدم لديه أحد أدوار الدعم النفسي المحددة في اللوحة
- إذا كان مخولاً: تظهر جميع الخيارات الأربعة
- إذا لم يكن مخولاً: يظهر خيار واحد فقط "غير مخول"

### 2. عند اختيار إجراء:
- يتم التحقق مرة أخرى من الصلاحيات قبل تنفيذ أي إجراء
- إذا لم يكن المستخدم مخولاً: يتم إرسال رسالة خطأ واضحة

### 3. في دوال إدارة التذاكر:
- طبقة حماية إضافية في كل دالة للتأكد من الصلاحيات
- منع تنفيذ الإجراءات حتى لو تم استدعاؤها مباشرة

## الفوائد:

✅ **أمان محسن**: منع المستخدمين غير المخولين من الوصول لخيارات إدارة التذاكر

✅ **تجربة مستخدم أفضل**: إخفاء الخيارات غير المتاحة بدلاً من إظهار رسائل خطأ

✅ **مرونة في الإعداد**: يمكن تحديد أدوار الدعم النفسي لكل لوحة بشكل منفصل

✅ **حماية متعددة الطبقات**: التحقق من الصلاحيات في عدة نقاط لضمان الأمان

## ملاحظات مهمة:

- يجب التأكد من تحديد أدوار الدعم النفسي بشكل صحيح في إعدادات اللوحة
- المستخدمون الذين لديهم صلاحيات الإدارة يمكنهم دائماً الوصول لجميع الخيارات
- النظام يعمل بشكل تلقائي ولا يحتاج إعداد إضافي

const { panelService } = require('../services');

/**
 * التحقق من أن المستخدم لديه أحد أدوار الدعم النفسي المحددة في اللوحة
 * @param {Object} interaction - تفاعل Discord
 * @param {string} channelId - معرف قناة التذكرة
 * @returns {Promise<boolean>} - true إذا كان المستخدم مخولاً، false إذا لم يكن
 */
async function hasTicketSupportRole(interaction, channelId) {
  try {
    // الحصول على بيانات التذكرة من قاعدة البيانات
    const ticket = await interaction.client.services.ticketService.getTicketByChannelId(channelId);
    
    if (!ticket) {
      console.warn('لم يتم العثور على التذكرة في قاعدة البيانات');
      return false;
    }

    // الحصول على بيانات اللوحة
    const panel = await panelService.getPanelById(ticket.panelId);
    
    if (!panel) {
      console.warn('لم يتم العثور على اللوحة في قاعدة البيانات');
      return false;
    }

    // التحقق من أن المستخدم لديه أحد أدوار الدعم النفسي
    const userHasSupportRole = panel.supportRoles.some(roleId => 
      interaction.member.roles.cache.has(roleId)
    );

    return userHasSupportRole;
  } catch (error) {
    console.error('Error checking support role permission:', error);
    return false;
  }
}

/**
 * التحقق من أن المستخدم لديه أحد أدوار الدعم النفسي المحددة في اللوحة (مع بيانات اللوحة المتوفرة)
 * @param {Object} interaction - تفاعل Discord
 * @param {Object} panel - بيانات اللوحة
 * @returns {boolean} - true إذا كان المستخدم مخولاً، false إذا لم يكن
 */
function hasTicketSupportRoleWithPanel(interaction, panel) {
  try {
    if (!panel || !panel.supportRoles) {
      return false;
    }

    // التحقق من أن المستخدم لديه أحد أدوار الدعم النفسي
    const userHasSupportRole = panel.supportRoles.some(roleId => 
      interaction.member.roles.cache.has(roleId)
    );

    return userHasSupportRole;
  } catch (error) {
    console.error('Error checking support role permission with panel:', error);
    return false;
  }
}

/**
 * إرسال رسالة خطأ للمستخدم غير المخول
 * @param {Object} interaction - تفاعل Discord
 * @param {Object} client - عميل Discord
 */
async function sendPermissionDeniedMessage(interaction, client) {
  try {
    const errorMessage = await client.translate(interaction.guild.id, 'errors.permission_denied') || 
                        'ليس لديك صلاحية لتنفيذ هذا الإجراء. هذه الخيارات متاحة فقط لأدوار الدعم النفسي المحددة.';
    
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    } else {
      await interaction.followUp({
        content: errorMessage,
        ephemeral: true
      });
    }
  } catch (error) {
    console.error('Error sending permission denied message:', error);
  }
}

module.exports = {
  hasTicketSupportRole,
  hasTicketSupportRoleWithPanel,
  sendPermissionDeniedMessage
};
